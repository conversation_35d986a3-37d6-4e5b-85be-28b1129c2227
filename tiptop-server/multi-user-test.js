#!/usr/bin/env node

/**
 * Test multiple users to see if they can see each other's messages
 */

const WebSocket = require('ws');

const WS_URL = process.env.WS_URL || 'ws://localhost:8080';
const TEST_URL = 'https://test-page.example.com';

console.log('🔍 Multi-User TipTop WebSocket Test');
console.log(`Connecting to: ${WS_URL}`);
console.log(`Test URL: ${TEST_URL}`);
console.log('');

// Create two users
const users = [
  { id: 'user-1', name: 'Alice' },
  { id: 'user-2', name: '<PERSON>' }
];

const connections = [];

async function createUser(user) {
  return new Promise((resolve, reject) => {
    const wsUrl = `${WS_URL}?url=${encodeURIComponent(TEST_URL)}`;
    const ws = new WebSocket(wsUrl);
    
    const userConnection = {
      user,
      ws,
      receivedMessages: []
    };
    
    ws.on('open', () => {
      console.log(`✅ ${user.name} connected`);
      
      // Send presence
      ws.send(JSON.stringify({
        type: 'presence',
        userId: user.id,
        userName: user.name,
        url: TEST_URL
      }));
      
      console.log(`📡 ${user.name} sent presence`);
      resolve(userConnection);
    });
    
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        userConnection.receivedMessages.push(message);
        
        if (message.type === 'chat') {
          console.log(`📨 ${user.name} received chat from ${message.userName}: "${message.content}"`);
        } else {
          console.log(`📨 ${user.name} received ${message.type} message`);
        }
      } catch (e) {
        console.log(`📨 ${user.name} received raw message:`, data.toString().substring(0, 50));
      }
    });
    
    ws.on('error', (error) => {
      console.error(`❌ ${user.name} WebSocket error:`, error.message);
      reject(error);
    });
    
    ws.on('close', () => {
      console.log(`🔌 ${user.name} disconnected`);
    });
  });
}

async function runTest() {
  try {
    // Connect both users
    console.log('Connecting users...');
    for (const user of users) {
      const connection = await createUser(user);
      connections.push(connection);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait between connections
    }
    
    console.log('✅ Both users connected');
    console.log('');
    
    // Wait for initial setup
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Alice sends a message
    console.log('💬 Alice sending message...');
    connections[0].ws.send(JSON.stringify({
      type: 'chat',
      content: 'Hello from Alice!',
      messageId: `alice-msg-${Date.now()}`,
      userId: 'user-1',
      userName: 'Alice',
      url: TEST_URL,
      timestamp: new Date().toISOString()
    }));
    
    // Wait for message to propagate
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Bob sends a message
    console.log('💬 Bob sending message...');
    connections[1].ws.send(JSON.stringify({
      type: 'chat',
      content: 'Hello from Bob!',
      messageId: `bob-msg-${Date.now()}`,
      userId: 'user-2',
      userName: 'Bob',
      url: TEST_URL,
      timestamp: new Date().toISOString()
    }));
    
    // Wait for message to propagate
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check results
    console.log('');
    console.log('📊 Test Results:');
    
    connections.forEach(conn => {
      const chatMessages = conn.receivedMessages.filter(m => m.type === 'chat');
      console.log(`${conn.user.name} received ${chatMessages.length} chat messages:`);
      chatMessages.forEach(msg => {
        console.log(`  - From ${msg.userName}: "${msg.content}"`);
      });
      console.log('');
    });
    
    // Check if cross-communication worked
    const aliceChatMessages = connections[0].receivedMessages.filter(m => m.type === 'chat');
    const bobChatMessages = connections[1].receivedMessages.filter(m => m.type === 'chat');
    
    const aliceReceivedFromBob = aliceChatMessages.some(m => m.userId === 'user-2');
    const bobReceivedFromAlice = bobChatMessages.some(m => m.userId === 'user-1');
    
    console.log('Cross-communication test:');
    console.log(`Alice received Bob's message: ${aliceReceivedFromBob ? '✅' : '❌'}`);
    console.log(`Bob received Alice's message: ${bobReceivedFromAlice ? '✅' : '❌'}`);
    
    if (aliceReceivedFromBob && bobReceivedFromAlice) {
      console.log('🎉 SUCCESS: Cross-communication is working!');
    } else {
      console.log('❌ FAILURE: Cross-communication is not working!');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Cleanup
    console.log('');
    console.log('🧹 Cleaning up...');
    connections.forEach(conn => {
      if (conn.ws.readyState === WebSocket.OPEN) {
        conn.ws.close();
      }
    });
    
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  }
}

// Run the test
runTest().catch(console.error);
