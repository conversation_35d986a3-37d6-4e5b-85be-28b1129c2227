#!/usr/bin/env node

/**
 * Test script to validate chat message ordering and persistence fixes
 * This script tests the WebSocket server's message handling capabilities
 */

const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid');

// Test configuration
const WS_URL = process.env.WS_URL || 'ws://localhost:8080';
const TEST_URL = 'https://test-page.example.com';
const NUM_CLIENTS = 3;
const MESSAGES_PER_CLIENT = 5;
const MESSAGE_DELAY = 100; // ms between messages

class ChatTestClient {
  constructor(clientId, testUrl) {
    this.clientId = clientId;
    this.userId = `test-user-${clientId}`;
    this.userName = `Test User ${clientId}`;
    this.testUrl = testUrl;
    this.ws = null;
    this.receivedMessages = [];
    this.sentMessages = [];
    this.connected = false;
  }

  async connect() {
    return new Promise((resolve, reject) => {
      const wsUrl = `${WS_URL}?url=${encodeURIComponent(this.testUrl)}`;
      console.log(`Client ${this.clientId}: Connecting to ${wsUrl}`);
      
      this.ws = new WebSocket(wsUrl);
      
      this.ws.on('open', () => {
        console.log(`Client ${this.clientId}: Connected`);
        this.connected = true;
        
        // Send presence message
        this.ws.send(JSON.stringify({
          type: 'presence',
          userId: this.userId,
          userName: this.userName,
          url: this.testUrl
        }));
        
        resolve();
      });
      
      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (e) {
          console.error(`Client ${this.clientId}: Error parsing message:`, e);
        }
      });
      
      this.ws.on('error', (error) => {
        console.error(`Client ${this.clientId}: WebSocket error:`, error);
        reject(error);
      });
      
      this.ws.on('close', () => {
        console.log(`Client ${this.clientId}: Disconnected`);
        this.connected = false;
      });
    });
  }

  handleMessage(message) {
    console.log(`Client ${this.clientId}: Received ${message.type} message`);
    
    if (message.type === 'chat') {
      this.receivedMessages.push({
        messageId: message.messageId,
        content: message.content,
        userId: message.userId,
        userName: message.userName,
        timestamp: message.timestamp,
        serverTimestamp: message.serverTimestamp,
        receivedAt: new Date().toISOString()
      });
    } else if (message.type === 'history_batch') {
      console.log(`Client ${this.clientId}: Received history batch ${message.batchIndex}/${message.totalBatches} with ${message.messages.length} messages`);
      message.messages.forEach(msg => {
        if (msg.type === 'chat') {
          this.receivedMessages.push({
            messageId: msg.messageId,
            content: msg.content,
            userId: msg.userId,
            userName: msg.userName,
            timestamp: msg.timestamp,
            serverTimestamp: msg.serverTimestamp,
            receivedAt: new Date().toISOString(),
            fromHistory: true
          });
        }
      });
    }
  }

  async sendMessage(content) {
    if (!this.connected) {
      throw new Error('Client not connected');
    }
    
    const messageId = `msg-${this.clientId}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const timestamp = new Date().toISOString();
    
    const message = {
      type: 'chat',
      content: content,
      messageId: messageId,
      userId: this.userId,
      userName: this.userName,
      url: this.testUrl,
      timestamp: timestamp
    };
    
    this.sentMessages.push(message);
    this.ws.send(JSON.stringify(message));
    
    console.log(`Client ${this.clientId}: Sent message "${content}"`);
    return messageId;
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }

  getStats() {
    return {
      clientId: this.clientId,
      sentCount: this.sentMessages.length,
      receivedCount: this.receivedMessages.length,
      receivedFromHistory: this.receivedMessages.filter(m => m.fromHistory).length,
      receivedLive: this.receivedMessages.filter(m => !m.fromHistory).length
    };
  }
}

async function runChatTest() {
  console.log('🚀 Starting chat message ordering and persistence test');
  console.log(`Testing with ${NUM_CLIENTS} clients, ${MESSAGES_PER_CLIENT} messages per client`);
  console.log(`WebSocket URL: ${WS_URL}`);
  console.log(`Test URL: ${TEST_URL}`);
  console.log('');

  const clients = [];
  
  try {
    // Create and connect clients
    console.log('📡 Creating and connecting clients...');
    for (let i = 1; i <= NUM_CLIENTS; i++) {
      const client = new ChatTestClient(i, TEST_URL);
      clients.push(client);
      await client.connect();
      await new Promise(resolve => setTimeout(resolve, 500)); // Stagger connections
    }
    
    console.log(`✅ All ${NUM_CLIENTS} clients connected`);
    console.log('');
    
    // Wait for initial setup
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Send messages from all clients
    console.log('💬 Sending messages from all clients...');
    const allMessageIds = [];
    
    for (let msgNum = 1; msgNum <= MESSAGES_PER_CLIENT; msgNum++) {
      for (let clientIdx = 0; clientIdx < clients.length; clientIdx++) {
        const client = clients[clientIdx];
        const content = `Message ${msgNum} from client ${client.clientId}`;
        const messageId = await client.sendMessage(content);
        allMessageIds.push(messageId);
        
        // Small delay between messages
        await new Promise(resolve => setTimeout(resolve, MESSAGE_DELAY));
      }
    }
    
    console.log(`📤 Sent ${allMessageIds.length} total messages`);
    console.log('');
    
    // Wait for message propagation
    console.log('⏳ Waiting for message propagation...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Analyze results
    console.log('📊 Analyzing results...');
    console.log('');
    
    // Print client stats
    clients.forEach(client => {
      const stats = client.getStats();
      console.log(`Client ${stats.clientId}:`);
      console.log(`  Sent: ${stats.sentCount} messages`);
      console.log(`  Received: ${stats.receivedCount} messages (${stats.receivedLive} live, ${stats.receivedFromHistory} from history)`);
      console.log('');
    });
    
    // Check message ordering
    console.log('🔍 Checking message ordering...');
    let orderingIssues = 0;
    
    clients.forEach(client => {
      const messages = client.receivedMessages.filter(m => !m.fromHistory);
      for (let i = 1; i < messages.length; i++) {
        const prev = messages[i - 1];
        const curr = messages[i];
        
        const prevTime = new Date(prev.serverTimestamp || prev.timestamp);
        const currTime = new Date(curr.serverTimestamp || curr.timestamp);
        
        if (prevTime > currTime) {
          console.log(`❌ Client ${client.clientId}: Message ordering issue detected`);
          console.log(`   Previous: ${prev.content} at ${prevTime.toISOString()}`);
          console.log(`   Current:  ${curr.content} at ${currTime.toISOString()}`);
          orderingIssues++;
        }
      }
    });
    
    if (orderingIssues === 0) {
      console.log('✅ No message ordering issues detected');
    } else {
      console.log(`❌ Found ${orderingIssues} message ordering issues`);
    }
    console.log('');
    
    // Check message delivery
    console.log('📬 Checking message delivery...');
    const expectedMessagesPerClient = NUM_CLIENTS * MESSAGES_PER_CLIENT;
    let deliveryIssues = 0;
    
    clients.forEach(client => {
      const liveMessages = client.receivedMessages.filter(m => !m.fromHistory);
      const expectedCount = expectedMessagesPerClient - client.sentMessages.length; // Don't count own messages
      
      if (liveMessages.length !== expectedCount) {
        console.log(`❌ Client ${client.clientId}: Expected ${expectedCount} messages, received ${liveMessages.length}`);
        deliveryIssues++;
      }
    });
    
    if (deliveryIssues === 0) {
      console.log('✅ All messages delivered correctly');
    } else {
      console.log(`❌ Found ${deliveryIssues} message delivery issues`);
    }
    console.log('');
    
    // Test reconnection and history
    console.log('🔄 Testing reconnection and message history...');
    const testClient = clients[0];
    testClient.disconnect();
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Reconnect and check history
    await testClient.connect();
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const historyMessages = testClient.receivedMessages.filter(m => m.fromHistory);
    console.log(`📚 Received ${historyMessages.length} messages from history after reconnection`);
    
    if (historyMessages.length > 0) {
      console.log('✅ Message history working correctly');
    } else {
      console.log('❌ No message history received after reconnection');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  } finally {
    // Cleanup
    console.log('🧹 Cleaning up...');
    clients.forEach(client => client.disconnect());
  }
  
  console.log('');
  console.log('🏁 Test completed');
}

// Run the test
if (require.main === module) {
  runChatTest().catch(console.error);
}

module.exports = { ChatTestClient, runChatTest };
