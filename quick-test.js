#!/usr/bin/env node

/**
 * Quick test to check if WebSocket server is working
 */

const WebSocket = require('ws');

const WS_URL = process.env.WS_URL || 'ws://localhost:8080';
const TEST_URL = 'https://test-page.example.com';

console.log('🔍 Quick TipTop WebSocket Test');
console.log(`Connecting to: ${WS_URL}`);
console.log(`Test URL: ${TEST_URL}`);
console.log('');

const wsUrl = `${WS_URL}?url=${encodeURIComponent(TEST_URL)}`;
const ws = new WebSocket(wsUrl);

let messageCount = 0;

ws.on('open', () => {
  console.log('✅ Connected to WebSocket server');
  
  // Send presence
  ws.send(JSON.stringify({
    type: 'presence',
    userId: 'test-user-1',
    userName: 'Test User 1',
    url: TEST_URL
  }));
  
  console.log('📡 Sent presence message');
  
  // Send a test chat message
  setTimeout(() => {
    ws.send(JSON.stringify({
      type: 'chat',
      content: 'Hello, this is a test message!',
      messageId: `test-msg-${Date.now()}`,
      userId: 'test-user-1',
      userName: 'Test User 1',
      url: TEST_URL,
      timestamp: new Date().toISOString()
    }));
    
    console.log('💬 Sent test chat message');
  }, 1000);
  
  // Request history
  setTimeout(() => {
    ws.send(JSON.stringify({
      type: 'get_history',
      url: TEST_URL,
      limit: 10
    }));
    
    console.log('📚 Requested message history');
  }, 2000);
  
  // Close after 5 seconds
  setTimeout(() => {
    console.log('');
    console.log(`📊 Test completed. Received ${messageCount} messages.`);
    ws.close();
  }, 5000);
});

ws.on('message', (data) => {
  messageCount++;
  try {
    const message = JSON.parse(data.toString());
    console.log(`📨 Received ${message.type} message:`, {
      type: message.type,
      content: message.content || `${message.messages?.length || 0} history messages`,
      userId: message.userId,
      userName: message.userName
    });
  } catch (e) {
    console.log('📨 Received raw message:', data.toString().substring(0, 100));
  }
});

ws.on('error', (error) => {
  console.error('❌ WebSocket error:', error.message);
});

ws.on('close', () => {
  console.log('🔌 WebSocket connection closed');
  process.exit(0);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n👋 Terminating test...');
  if (ws.readyState === WebSocket.OPEN) {
    ws.close();
  }
  process.exit(0);
});
